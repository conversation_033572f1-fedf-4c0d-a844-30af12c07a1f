.filter-presets {
  .preset-actions {
    display: flex;
    align-items: center;
    gap: 8px;

    .presets-button {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .save-preset-button {
      color: #1976d2;
    }
  }
}

.presets-menu {
  min-width: 300px;

  .preset-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;

    .preset-title {
      font-weight: 500;
      color: #333;
    }
  }

  .preset-list {
    max-height: 300px;
    overflow-y: auto;

    .preset-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      width: 100%;
      text-align: left;

      mat-icon {
        color: #666;
      }

      .preset-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 2px;

        .preset-name {
          font-weight: 500;
          color: #333;
        }

        .preset-description {
          font-size: 12px;
          color: #666;
        }
      }

      .delete-preset {
        opacity: 0;
        transition: opacity 0.2s;

        mat-icon {
          color: #f44336;
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }

      &:hover .delete-preset {
        opacity: 1;
      }
    }
  }

  .preset-footer {
    border-top: 1px solid #e0e0e0;
    padding-top: 8px;

    .clear-presets {
      color: #f44336;
      width: 100%;
    }
  }
}
