.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 20px;
}

.error-card {
  max-width: 500px;
  width: 100%;
  text-align: center;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.error-logo {
  .logo-image {
    height: 40px;
    width: auto;
    opacity: 0.7;
  }
}

.error-icon {
  mat-icon {
    font-size: 4rem;
    width: 4rem;
    height: 4rem;
  }

  &.error-error mat-icon {
    color: #f44336;
  }

  &.error-warning mat-icon {
    color: #ff9800;
  }

  &.error-info mat-icon {
    color: #2196f3;
  }

  &.error-not-found mat-icon {
    color: #9e9e9e;
  }
}

.error-text {
  .error-title {
    margin: 0 0 12px 0;
    font-size: 24px;
    font-weight: 500;
    color: #333;
  }

  .error-message {
    margin: 0;
    font-size: 16px;
    color: #666;
    line-height: 1.5;
  }
}

.error-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

// Mobile responsive
@media (max-width: 480px) {
  .error-container {
    padding: 10px;
    min-height: 300px;
  }

  .error-content {
    padding: 16px;
    gap: 16px;
  }

  .error-icon mat-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
  }

  .error-text .error-title {
    font-size: 20px;
  }

  .error-actions {
    flex-direction: column;
    width: 100%;

    button {
      width: 100%;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .error-text {
    .error-title {
      color: #fff;
    }

    .error-message {
      color: #ccc;
    }
  }
}
